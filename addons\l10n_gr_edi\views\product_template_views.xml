<?xml version="1.0"?>
<odoo>

    <record id="product_template_form_view_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">product.template.form.inherit.l10n.gr.edi</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="account.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='invoicing']//group[@name='accounting']" position="after">
                <group name="mydata" string="MyDATA" invisible="'GR' not in fiscal_country_codes">
                    <field name="l10n_gr_edi_preferred_classification_ids">
                        <list editable="bottom" default_order="priority desc">
                            <field name="priority" widget="handle"/>
                            <field name="l10n_gr_edi_available_inv_type" column_invisible="1"/>
                            <field name="l10n_gr_edi_available_cls_category" column_invisible="1"/>
                            <field name="l10n_gr_edi_available_cls_type" column_invisible="1"/>
                            <field name="l10n_gr_edi_inv_type"
                                   widget="dynamic_selection"
                                   options="{'available_field': 'l10n_gr_edi_available_inv_type'}"/>
                            <field name="l10n_gr_edi_cls_category"
                                   widget="dynamic_selection"
                                   options="{'available_field': 'l10n_gr_edi_available_cls_category'}"/>
                            <field name="l10n_gr_edi_cls_type"
                                   widget="dynamic_selection"
                                   options="{'available_field': 'l10n_gr_edi_available_cls_type'}"/>
                        </list>
                    </field>
                </group>
            </xpath>
        </field>
    </record>

</odoo>
