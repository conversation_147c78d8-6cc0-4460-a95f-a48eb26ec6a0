import pyodbc
import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine
import psycopg2

# 配置开关：是否启用更新销售订单状态
ENABLE_STATUS_UPDATE = False  # 设置为 True 启用，设置为 False 禁用

# 配置开关：是否启用文件导出
ENABLE_FILE_EXPORT = False  # 设置为 True 启用，设置为 False 禁用

# 配置开关：是否关联invoice和sale order
ENABLE_INVOICE_SO_LINKING = True # 设置为 True 启用，设置为 False 禁用

# 数据库连接配置
# python310 odoo-imports-bh/sales_invoice_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
SQL_SERVER_CONFIG = {
    'server': '**************',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

# PostgreSQL 数据库连接配置
PG_CONFIG = {
    'dbname': "brighthome_odoo18_live",
    'user': "bhsa",
    'password': "Bh44Patiki@Akl",
    'host': "bh-postgresql-server.postgres.database.azure.com",
    'port': "5432"
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def connect_to_postgresql():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        print("成功连接到PostgreSQL数据库")
        return conn
    except Exception as e:
        print(f"连接PostgreSQL数据库时出错: {e}")
        return None

def fetch_invoice_data(conn):
    """从SQL Server获取销售发票数据"""
    query = """
    SET DATEFORMAT ymd;
    SELECT
        ISNULL(NULLIF(RTRIM(LTRIM(c3.trading_name)), ''), 'Unknown Customer') AS 'Partner',
        i.commit_date AS 'Invoice/Bill Date',
        i.commit_date AS 'Due Date',
        c1.name AS Salesperson,
        o.id AS 'Source',
        i.invoice_number AS 'Number',
        ISNULL(NULLIF(cr.name, ''), 'Unknown Item') AS 'Invoice Lines/Product',
        s.quantity AS 'Invoice Lines/Quantity',
        s.commit_price AS 'Invoice Lines/Unit Price'
    from invoice i
    left join orders o on o.invoice_number = i.invoice_number
    left join sales s on s.invoice_number = i.invoice_number
    left join code_relations cr on cr.code = s.code
    left join card c1 on c1.id = o.sales
    left join card c2 on c2.id = o.sales_manager
    left join card c3 on c3.id = o.card_id and c3.type = 2 AND c3.id <> 0
    --where i.commit_date < DATEADD(YEAR, -5, GETDATE())
	--and i.commit_date >= DATEADD(YEAR, -7, GETDATE())
	--order by commit_date
    --where i.commit_date >= DATEADD(YEAR, -1, GETDATE());
    --where o.record_date <= '2025-01-01'
    where i.invoice_number = '101315'
    """
    #where i.commit_date >= DATEADD(YEAR, -5, GETDATE());
    #where i.commit_date >= DATEADD(MONTH, -6, GETDATE());
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条销售发票记录")
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def update_sale_order_state(conn, source, success_log_file, error_log_file):
    """更新PostgreSQL数据库中的销售订单状态
    Args:
        conn: PostgreSQL数据库连接
        source: 销售订单编号
        success_log_file: 成功日志文件路径
        error_log_file: 失败日志文件路径
    """
    cursor = None
    update_query = f"""
    UPDATE sale_order
    SET state = 'sale', invoice_status = 'invoiced'
    WHERE name = '{source}'
    """
    try:
        cursor = conn.cursor()
        print(f"正在执行SQL更新状态: {update_query}")
        cursor.execute(update_query)
        conn.commit()

        # 记录成功日志
        with open(success_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 成功更新销售订单 {source} 的状态\n")
            f.write(f"执行的SQL: {update_query}\n\n")

        return True
    except Exception as e:
        # 记录失败日志
        with open(error_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 更新销售订单 {source} 的状态时出错: {e}\n")
            f.write(f"执行的SQL: {update_query}\n\n")

        conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def link_invoice_and_so(conn, source, number, success_log_file, error_log_file):
    """关联PostgreSQL数据库中的销售发票和销售订单
    Args:
        conn: PostgreSQL数据库连接
        source: 销售订单编号 (e.g., S012345)
        number: 销售发票编号 (e.g., INV012345)
        success_log_file: 成功日志文件路径
        error_log_file: 失败日志文件路径
    """
    # 如果source为空，直接跳过
    if not source or source.strip() == '':
        print(f"销售订单编号为空，跳过关联")
        # 记录跳过日志
        with open(success_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 销售订单编号为空，跳过与发票 {number} 的关联\n\n")
        return True

    cursor = None
    sale_order_id = None
    account_move_id = None
    sql_executed = []
    relations_created = 0

    try:
        cursor = conn.cursor()

        # 第一步：查询销售订单ID
        print(f"开始关联: SO {source}, Invoice {number}")
        query_so_id = f"SELECT id FROM sale_order WHERE name = '{source}'"
        sql_executed.append(query_so_id)
        cursor.execute(query_so_id)
        so_result = cursor.fetchone()
        if not so_result:
            raise Exception(f"未找到销售订单 {source}")
        sale_order_id = so_result[0]

        # 第二步：查询发票ID
        query_invoice_id = f"SELECT id FROM account_move WHERE name = '{number}' AND move_type = 'in_invoice'"
        sql_executed.append(query_invoice_id)
        cursor.execute(query_invoice_id)
        invoice_result = cursor.fetchone()
        if not invoice_result:
            raise Exception(f"未找到销售发票 {number}")
        account_move_id = invoice_result[0]

        # 第三步：查询销售订单行IDs、产品名称和数量
        query_so_line_ids = f"""
            SELECT
                sol.id as order_line_id,
                sol.name,
                sol.product_uom_qty as quantity
            FROM sale_order_line sol
            WHERE sol.order_id = {sale_order_id}
        """
        sql_executed.append(query_so_line_ids)
        cursor.execute(query_so_line_ids)
        so_line_results = cursor.fetchall()
        if not so_line_results:
            raise Exception(f"未找到销售订单行，销售订单ID: {sale_order_id}")

        # 第四步：查询发票行IDs、产品名称和数量
        query_invoice_line_ids = f"""
            SELECT
                aml.id as invoice_line_id,
                aml.name,
                aml.quantity
            FROM account_move_line aml
            WHERE aml.move_id = {account_move_id} AND aml.product_id IS NOT NULL
        """
        sql_executed.append(query_invoice_line_ids)
        cursor.execute(query_invoice_line_ids)
        invoice_line_results = cursor.fetchall()
        if not invoice_line_results:
            raise Exception(f"未找到发票行，发票ID: {account_move_id}")

        # 第五步：基于产品名称和数量匹配销售订单行和发票行
        print(f"销售订单行数量: {len(so_line_results)}, 发票行数量: {len(invoice_line_results)}")

        # 创建产品名称和数量到行ID的映射
        so_lines_by_product = {}
        for so_line in so_line_results:
            order_line_id, product_name, quantity = so_line
            # 使用产品名称和数量作为键
            key = (product_name, quantity)
            if key not in so_lines_by_product:
                so_lines_by_product[key] = []
            so_lines_by_product[key].append((order_line_id, product_name))

        invoice_lines_by_product = {}
        for inv_line in invoice_line_results:
            invoice_line_id, product_name, quantity = inv_line
            # 使用产品名称和数量作为键
            key = (product_name, quantity)
            if key not in invoice_lines_by_product:
                invoice_lines_by_product[key] = []
            invoice_lines_by_product[key].append((invoice_line_id, product_name))

        # 基于产品名称和数量匹配
        for key, so_lines in so_lines_by_product.items():
            product_name, quantity = key
            if key in invoice_lines_by_product:
                inv_lines = invoice_lines_by_product[key]

                # 使用最小长度确保一对一匹配
                match_count = min(len(so_lines), len(inv_lines))
                for i in range(match_count):
                    order_line_id, so_product_name = so_lines[i]
                    invoice_line_id, inv_product_name = inv_lines[i]

                    # 检查关联是否已存在
                    check_rel_query = f"SELECT 1 FROM sale_order_line_invoice_rel WHERE order_line_id = {order_line_id} AND invoice_line_id = {invoice_line_id}"
                    sql_executed.append(check_rel_query)
                    cursor.execute(check_rel_query)

                    if cursor.fetchone():
                        print(f"关联已存在: SO Line ID {order_line_id} (产品: {so_product_name}), Invoice Line ID {invoice_line_id} (产品: {inv_product_name})")
                    else:
                        # 插入关联记录
                        insert_rel_query = f"INSERT INTO sale_order_line_invoice_rel (order_line_id, invoice_line_id) VALUES ({order_line_id}, {invoice_line_id})"
                        sql_executed.append(insert_rel_query)
                        cursor.execute(insert_rel_query)
                        relations_created += 1
                        print(f"成功插入关联: SO Line ID {order_line_id} (产品: {so_product_name}, 数量: {quantity}), Invoice Line ID {invoice_line_id} (产品: {inv_product_name})")
            else:
                print(f"警告: 未找到匹配的发票行，产品名称: {product_name}, 数量: {quantity}")

        conn.commit()

        # 记录成功日志
        with open(success_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 成功关联 SO {source} (ID: {sale_order_id}) 和 Invoice {number} (ID: {account_move_id})\n")
            f.write(f"创建了 {relations_created} 个新关联\n")
            for sql in sql_executed:
                f.write(f"  执行的SQL: {sql.strip()}\n")
            f.write("\n")

        return True
    except Exception as e:
        # 记录失败日志
        with open(error_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 关联 SO {source} 和 Invoice {number} 时出错: {e}\n")
            for sql in sql_executed:
                f.write(f"  尝试执行的SQL: {sql.strip()}\n")
            f.write("\n")

        conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def convert_to_odoo_format(df, pg_conn=None):
    """将数据转换为Odoo销售发票导入格式"""

    # 添加Odoo需要的额外字段并放在前列
    #df.insert(0, 'Company', 'Bright Home Electrical')
    #df.insert(1, 'Status', 'Draft')

    # 格式化日期列为仅日期格式（YYYY-MM-DD）
    if 'Invoice/Bill Date' in df.columns:
        df['Invoice/Bill Date'] = pd.to_datetime(df['Invoice/Bill Date']).dt.strftime('%Y-%m-%d')

    if 'Due Date' in df.columns:
        df['Due Date'] = pd.to_datetime(df['Due Date']).dt.strftime('%Y-%m-%d')

    # 添加tax列，默认值15%
    df['Invoice Lines/Taxes'] = '15%'

    # 为Source添加S前缀，为Number添加INV前缀，并确保没有小数点
    if 'Source' in df.columns:
        # 过滤掉Source列中值为"nan"的行
        mask = df['Source'].astype(str) != "nan"

        # 对于非"nan"的值，添加前缀并转换为整数（去掉小数点）
        df.loc[mask, 'Source'] = 'S' + df.loc[mask, 'Source'].astype(float).astype(int).astype(str)
        df.loc[mask, 'Number'] = 'INV' + df.loc[mask, 'Number'].astype(float).astype(int).astype(str)

        # 对于"nan"的值，设置默认值
        df.loc[~mask, 'Source'] = 'S0'
        df.loc[~mask, 'Number'] = 'INV0'

    # 重命名列以匹配Odoo模板
    column_mapping = {
        'Partner': 'Partner',
        'Invoice/Bill Date': 'Invoice/Bill Date',
        'Due Date': 'Due Date',
        'Salesperson': 'Salesperson',
        'Source': 'Source',
        'Number': 'Number',
        'Invoice Lines/Product': 'Invoice Lines/Product',
        'Invoice Lines/Quantity': 'Invoice Lines/Quantity',
        'Invoice Lines/Unit Price': 'Invoice Lines/Unit Price',
        'Invoice Lines/Taxes': 'Invoice Lines/Taxes',
    }

    df = df.rename(columns=column_mapping)

    # 分组处理相同Source的记录
    if 'Source' in df.columns:
        # 定义需要保留的字段
        keep_cols = ['Invoice Lines/Product', 'Invoice Lines/Quantity', 'Invoice Lines/Unit Price', 'Invoice Lines/Taxes']

        # 创建一个新的DataFrame来存储处理后的数据
        result_df = pd.DataFrame()

        # 获取所有唯一的订单号
        source_refs = df['Source'].unique()

        # 遍历每个订单
        for source_ref in source_refs:
            # 获取当前订单的所有行
            source_df = df[df['Source'] == source_ref].copy()

            if len(source_df) > 1:
                # 获取第一条记录（包含订单头信息）
                first_row = source_df.iloc[0:1].copy()

                # 获取后续记录（只包含行项目信息）
                other_rows = source_df.iloc[1:].copy()

                # 清除非保留字段
                for col in other_rows.columns:
                    if col not in keep_cols:
                        other_rows[col] = ''

                # 合并第一条记录和后续记录
                processed_source = pd.concat([first_row, other_rows], ignore_index=True)
            else:
                # 如果只有一行，直接使用
                processed_source = source_df

            # 添加到结果DataFrame
            result_df = pd.concat([result_df, processed_source], ignore_index=True)

        # 使用处理后的DataFrame替换原始DataFrame
        df = result_df

    # 根据开关决定是否更新销售订单状态
    if ENABLE_STATUS_UPDATE and pg_conn:
        for source in df['Source'].unique():
            update_sale_order_state(pg_conn, source, 'success_log.txt', 'error_log.txt')

    # 根据开关决定是否关联销售发票和销售订单
    if ENABLE_INVOICE_SO_LINKING and pg_conn:
        for source, number in zip(df['Source'], df['Number']):
            link_invoice_and_so(pg_conn, source, number, 'success_log.txt', 'error_log.txt')

    return df

def save_to_file(df, filename='sales_invoice_template'):
    """将数据保存为CSV和Excel文件，按20000条记录拆分到不同文件"""
    if not ENABLE_FILE_EXPORT:
        print("文件导出功能已禁用")
        return

    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)

        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {backups_dir}")

        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件到backups文件夹: {src} -> {dst}")

        # 按20000条记录拆分数据
        total_rows = len(df)
        file_count = (total_rows + 19999) // 20000  # 向上取整

        for i in range(file_count):
            start_idx = i * 20000
            end_idx = min((i + 1) * 20000, total_rows)
            chunk_df = df.iloc[start_idx:end_idx]

            # 生成文件名
            file_suffix = f"_{i+1}_of_{file_count}_{timestamp}"

            # 保存CSV文件
            csv_path = os.path.join(script_dir, f"{filename}{file_suffix}.csv")
            chunk_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"已保存CSV文件: {csv_path}，包含{len(chunk_df)}行数据")

            # 保存Excel文件
            excel_path = os.path.join(script_dir, f"{filename}{file_suffix}.xlsx")
            chunk_df.to_excel(excel_path, index=False)
            print(f"已保存Excel文件: {excel_path}，包含{len(chunk_df)}行数据")

    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 1. 连接数据库
    engine = connect_to_sql_server()
    pg_conn = connect_to_postgresql()

    if engine:
        # 2. 获取数据
        df = fetch_invoice_data(engine)
        if df is not None:
            # 3. 转换格式并根据开关执行数据库操作
            odoo_df = convert_to_odoo_format(df, pg_conn)
            # 4. 根据开关决定是否保存为CSV和Excel
            save_to_file(odoo_df)
        engine.dispose()

    if pg_conn:
        pg_conn.close()
        print("已关闭PostgreSQL连接")