[2025-05-26 03:54:43] 成功关联 PO P07351 (ID: 30223) 和 Bill B07351 (ID: 224322)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07351'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07351' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30223 AND account_move_id = 224322
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30223, 224322)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30223
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30223
            WHERE aml.move_id = 224322
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] PO P07359 (ID: 30231) 和 Bill B07359 (ID: 221542) 的关联已存在，跳过插入。
[2025-05-26 03:54:43] 成功关联 PO P07359 (ID: 30231) 和 Bill B07359 (ID: 221542)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07359'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07359' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30231 AND account_move_id = 221542
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30231
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30231
            WHERE aml.move_id = 221542
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07376 (ID: 30248) 和 Bill B07376 (ID: 224324)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07376'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07376' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30248 AND account_move_id = 224324
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30248, 224324)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30248
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30248
            WHERE aml.move_id = 224324
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07379 (ID: 30251) 和 Bill B07379 (ID: 224325)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07379'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07379' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30251 AND account_move_id = 224325
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30251, 224325)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30251
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30251
            WHERE aml.move_id = 224325
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] PO P07387 (ID: 30259) 和 Bill B07387 (ID: 221543) 的关联已存在，跳过插入。
[2025-05-26 03:54:43] 成功关联 PO P07387 (ID: 30259) 和 Bill B07387 (ID: 221543)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07387'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07387' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30259 AND account_move_id = 221543
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30259
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30259
            WHERE aml.move_id = 221543
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07394 (ID: 30266) 和 Bill B07394 (ID: 224327)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07394'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07394' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30266 AND account_move_id = 224327
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30266, 224327)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30266
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30266
            WHERE aml.move_id = 224327
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07399 (ID: 30271) 和 Bill B07399 (ID: 224328)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07399'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07399' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30271 AND account_move_id = 224328
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30271, 224328)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30271
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30271
            WHERE aml.move_id = 224328
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07401 (ID: 30273) 和 Bill B07401 (ID: 224329)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07401'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07401' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30273 AND account_move_id = 224329
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30273, 224329)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30273
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30273
            WHERE aml.move_id = 224329
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07407 (ID: 30279) 和 Bill B07407 (ID: 224330)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07407'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07407' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30279 AND account_move_id = 224330
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30279, 224330)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30279
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30279
            WHERE aml.move_id = 224330
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07409 (ID: 30281) 和 Bill B07409 (ID: 224331)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07409'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07409' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30281 AND account_move_id = 224331
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30281, 224331)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30281
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30281
            WHERE aml.move_id = 224331
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07410 (ID: 30282) 和 Bill B07410 (ID: 224332)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07410'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07410' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30282 AND account_move_id = 224332
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30282, 224332)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30282
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30282
            WHERE aml.move_id = 224332
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07412 (ID: 30284) 和 Bill B07412 (ID: 224333)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07412'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07412' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30284 AND account_move_id = 224333
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30284, 224333)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30284
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30284
            WHERE aml.move_id = 224333
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07419 (ID: 30291) 和 Bill B07419 (ID: 224334)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07419'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07419' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30291 AND account_move_id = 224334
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30291, 224334)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30291
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30291
            WHERE aml.move_id = 224334
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:43] 成功关联 PO P07422 (ID: 30294) 和 Bill B07422 (ID: 224335)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07422'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07422' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30294 AND account_move_id = 224335
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30294, 224335)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30294
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30294
            WHERE aml.move_id = 224335
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07428 (ID: 30300) 和 Bill B07428 (ID: 224336)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07428'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07428' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30300 AND account_move_id = 224336
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30300, 224336)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30300
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30300
            WHERE aml.move_id = 224336
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07434 (ID: 30306) 和 Bill B07434 (ID: 224337)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07434'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07434' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30306 AND account_move_id = 224337
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30306, 224337)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30306
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30306
            WHERE aml.move_id = 224337
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07441 (ID: 30313) 和 Bill B07441 (ID: 224338)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07441'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07441' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30313 AND account_move_id = 224338
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30313, 224338)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30313
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30313
            WHERE aml.move_id = 224338
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07444 (ID: 30316) 和 Bill B07444 (ID: 224339)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07444'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07444' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30316 AND account_move_id = 224339
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30316, 224339)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30316
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30316
            WHERE aml.move_id = 224339
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07446 (ID: 30318) 和 Bill B07446 (ID: 224340)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07446'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07446' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30318 AND account_move_id = 224340
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30318, 224340)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30318
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30318
            WHERE aml.move_id = 224340
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07448 (ID: 30320) 和 Bill B07448 (ID: 224341)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07448'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07448' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30320 AND account_move_id = 224341
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30320, 224341)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30320
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30320
            WHERE aml.move_id = 224341
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07450 (ID: 30322) 和 Bill B07450 (ID: 224342)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07450'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07450' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30322 AND account_move_id = 224342
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30322, 224342)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30322
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30322
            WHERE aml.move_id = 224342
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07451 (ID: 30323) 和 Bill B07451 (ID: 224343)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07451'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07451' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30323 AND account_move_id = 224343
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30323, 224343)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30323
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30323
            WHERE aml.move_id = 224343
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07452 (ID: 30324) 和 Bill B07452 (ID: 224344)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07452'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07452' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30324 AND account_move_id = 224344
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30324, 224344)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30324
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30324
            WHERE aml.move_id = 224344
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07455 (ID: 30327) 和 Bill B07455 (ID: 224345)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07455'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07455' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30327 AND account_move_id = 224345
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30327, 224345)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30327
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30327
            WHERE aml.move_id = 224345
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07460 (ID: 30332) 和 Bill B07460 (ID: 224346)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07460'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07460' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30332 AND account_move_id = 224346
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30332, 224346)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30332
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30332
            WHERE aml.move_id = 224346
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07461 (ID: 30333) 和 Bill B07461 (ID: 224347)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07461'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07461' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30333 AND account_move_id = 224347
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30333, 224347)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30333
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30333
            WHERE aml.move_id = 224347
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07462 (ID: 30334) 和 Bill B07462 (ID: 224348)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07462'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07462' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30334 AND account_move_id = 224348
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30334, 224348)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30334
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30334
            WHERE aml.move_id = 224348
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07464 (ID: 30336) 和 Bill B07464 (ID: 224349)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07464'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07464' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30336 AND account_move_id = 224349
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30336, 224349)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30336
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30336
            WHERE aml.move_id = 224349
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07469 (ID: 30341) 和 Bill B07469 (ID: 224350)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07469'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07469' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30341 AND account_move_id = 224350
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30341, 224350)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30341
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30341
            WHERE aml.move_id = 224350
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07470 (ID: 30342) 和 Bill B07470 (ID: 224351)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07470'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07470' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30342 AND account_move_id = 224351
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30342, 224351)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30342
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30342
            WHERE aml.move_id = 224351
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07471 (ID: 30343) 和 Bill B07471 (ID: 224352)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07471'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07471' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30343 AND account_move_id = 224352
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30343, 224352)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30343
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30343
            WHERE aml.move_id = 224352
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07477 (ID: 30349) 和 Bill B07477 (ID: 224353)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07477'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07477' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30349 AND account_move_id = 224353
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30349, 224353)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30349
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30349
            WHERE aml.move_id = 224353
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] PO P07478 (ID: 30350) 和 Bill B07478 (ID: 221550) 的关联已存在，跳过插入。
[2025-05-26 03:54:44] 成功关联 PO P07478 (ID: 30350) 和 Bill B07478 (ID: 221550)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07478'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07478' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30350 AND account_move_id = 221550
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30350
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30350
            WHERE aml.move_id = 221550
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07479 (ID: 30351) 和 Bill B07479 (ID: 224355)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07479'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07479' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30351 AND account_move_id = 224355
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30351, 224355)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30351
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30351
            WHERE aml.move_id = 224355
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:44] 成功关联 PO P07483 (ID: 30355) 和 Bill B07483 (ID: 224356)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07483'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07483' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30355 AND account_move_id = 224356
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30355, 224356)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30355
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30355
            WHERE aml.move_id = 224356
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:45] 成功关联 PO P07486 (ID: 30358) 和 Bill B07486 (ID: 224357)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07486'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07486' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30358 AND account_move_id = 224357
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30358, 224357)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30358
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30358
            WHERE aml.move_id = 224357
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:45] 成功关联 PO P07489 (ID: 30361) 和 Bill B07489 (ID: 224358)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07489'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07489' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30361 AND account_move_id = 224358
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30361, 224358)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30361
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30361
            WHERE aml.move_id = 224358
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:45] 成功关联 PO P07490 (ID: 30362) 和 Bill B07490 (ID: 224359)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07490'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07490' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30362 AND account_move_id = 224359
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30362, 224359)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30362
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30362
            WHERE aml.move_id = 224359
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;

[2025-05-26 03:54:45] 成功关联 PO P07513 (ID: 30526) 和 Bill B07513 (ID: 224360)
  执行的SQL: SELECT id FROM purchase_order WHERE name = 'P07513'
  执行的SQL: SELECT id FROM account_move WHERE name = 'B07513' AND move_type = 'in_invoice'
  执行的SQL: SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = 30526 AND account_move_id = 224360
  执行的SQL: INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES (30526, 224360)
  执行的SQL: UPDATE purchase_order SET invoice_count = 1 WHERE id = 30526
  执行的SQL: WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = 30526
            WHERE aml.move_id = 224360
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;


总结统计信息:
成功关联Bill和PO的数量: 39
关联Bill和PO失败的数量: 0
