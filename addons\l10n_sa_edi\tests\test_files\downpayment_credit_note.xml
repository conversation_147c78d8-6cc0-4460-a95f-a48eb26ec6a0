<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2">
  <cbc:UBLVersionID>2.1</cbc:UBLVersionID>
  <cbc:ProfileID>reporting:1.0</cbc:ProfileID>
  <cbc:ID>RINV/2022/00001</cbc:ID>
  <cbc:UUID>ea8e1ab4-6b4e-4cb2-8efc-f8e229622774</cbc:UUID>
  <cbc:IssueDate>2022-09-05</cbc:IssueDate>
  <cbc:IssueTime>08:20:02</cbc:IssueTime>
  <cbc:InvoiceTypeCode name="0100100">381</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>SAR</cbc:DocumentCurrencyCode>
  <cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>
  <cbc:BuyerReference>Azure Interior</cbc:BuyerReference>
  <cac:OrderReference>
    <cbc:ID>Reversal of: INV/2022/00001, please reverse :c</cbc:ID>
  </cac:OrderReference>
  <cac:BillingReference>
    <cac:InvoiceDocumentReference>
      <cbc:ID>INV/2022/00001</cbc:ID>
    </cac:InvoiceDocumentReference>
  </cac:BillingReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>PIH</cbc:ID>
    <cac:Attachment>
      <cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==</cbc:EmbeddedDocumentBinaryObject>
    </cac:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>ICV</cbc:ID>
    <cbc:UUID>0</cbc:UUID>
  </cac:AdditionalDocumentReference>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="CRN">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>SA Company Test</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
        <cbc:BuildingNumber>1234</cbc:BuildingNumber>
        <cbc:PlotIdentification>1234</cbc:PlotIdentification>
        <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
        <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
        <cbc:PostalZone>42317</cbc:PostalZone>
        <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
        <cac:Country>
          <cbc:IdentificationCode>SA</cbc:IdentificationCode>
          <cbc:Name>Saudi Arabia</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>SA Company Test</cbc:RegistrationName>
        <cbc:CompanyID>311111111111113</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
          <cbc:BuildingNumber>1234</cbc:BuildingNumber>
          <cbc:PlotIdentification>1234</cbc:PlotIdentification>
          <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
          <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
          <cbc:PostalZone>42317</cbc:PostalZone>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>SA Company Test</cbc:RegistrationName>
        <cbc:CompanyID>311111111111113</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
          <cbc:BuildingNumber>1234</cbc:BuildingNumber>
          <cbc:PlotIdentification>1234</cbc:PlotIdentification>
          <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
          <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
          <cbc:PostalZone>42317</cbc:PostalZone>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>346</cbc:ID>
        <cbc:Name>SA Company Test</cbc:Name>
        <cbc:Telephone>+************</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="CRN">US12345677</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Chichi Lboukla</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>4557 De Silva St</cbc:StreetName>
        <cbc:BuildingNumber>12300</cbc:BuildingNumber>
        <cbc:PlotIdentification>2323</cbc:PlotIdentification>
        <cbc:CitySubdivisionName>Neighbor!</cbc:CitySubdivisionName>
        <cbc:CityName>Fremont</cbc:CityName>
        <cbc:PostalZone>94538</cbc:PostalZone>
        <cbc:CountrySubentity>California</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>CA</cbc:CountrySubentityCode>
        <cac:Country>
          <cbc:IdentificationCode>US</cbc:IdentificationCode>
          <cbc:Name>United States</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Chichi Lboukla</cbc:RegistrationName>
        <cac:RegistrationAddress>
          <cbc:StreetName>4557 De Silva St</cbc:StreetName>
          <cbc:BuildingNumber>12300</cbc:BuildingNumber>
          <cbc:PlotIdentification>2323</cbc:PlotIdentification>
          <cbc:CitySubdivisionName>Neighbor!</cbc:CitySubdivisionName>
          <cbc:CityName>Fremont</cbc:CityName>
          <cbc:PostalZone>94538</cbc:PostalZone>
          <cbc:CountrySubentity>California</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>CA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>US</cbc:IdentificationCode>
            <cbc:Name>United States</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>350</cbc:ID>
        <cbc:Name>Chichi Lboukla</cbc:Name>
        <cbc:Telephone>+***********</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cbc:ActualDeliveryDate>2022-09-05</cbc:ActualDeliveryDate>
  </cac:Delivery>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode listID="UN/ECE 4461">1</cbc:PaymentMeansCode>
    <cbc:PaymentDueDate>2022-09-22</cbc:PaymentDueDate>
    <cbc:InstructionNote>Reversal of: INV/2022/00001, please reverse :c</cbc:InstructionNote>
    <cbc:PaymentID>RINV/2022/00001</cbc:PaymentID>
  </cac:PaymentMeans>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="SAR">15.00</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="SAR">100.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="SAR">15.00</cbc:TaxAmount>
      <cbc:Percent>15.0</cbc:Percent>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>15.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="SAR">15.00</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="SAR">100.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="SAR">100.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="SAR">115.00</cbc:TaxInclusiveAmount>
    <cbc:AllowanceTotalAmount currencyID="SAR">0.00</cbc:AllowanceTotalAmount>
    <cbc:PrepaidAmount currencyID="SAR">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="SAR">115.00</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>1</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="SAR">100.00</cbc:LineExtensionAmount>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="SAR">15.00</cbc:TaxAmount>
      <cbc:RoundingAmount currencyID="SAR">115.00</cbc:RoundingAmount>
    </cac:TaxTotal>
    <cac:Item>
      <cbc:Description>Down Payment</cbc:Description>
      <cbc:Name>Down Payment</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>15.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="SAR">100.0</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
</Invoice>
