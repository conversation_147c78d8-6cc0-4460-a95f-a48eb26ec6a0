import pyodbc
import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine

# 数据库连接配置
# test2，des为空的
# python310 odoo-imports-bh/purchase_order_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
SQL_SERVER_CONFIG = {
    'server': '192.168.10.214',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def fetch_purchase_data(conn):
    """从SQL Server获取采购数据"""
    query = """
    SET DATEFORMAT ymd;
    SELECT RTRIM(LTRIM(c2.trading_name)) AS Vendor, p.date_create AS 'Order Deadline', c1.name AS Buyer, '' AS 'Source Document'
    , p.total AS Total, p.po_number AS 'Order Reference'
    , CASE WHEN pi.name IS NOT NULL AND pi.name != '' THEN pi.name ELSE cr.name END AS 'Order Lines/Description'
    , CASE WHEN cr.name IS NOT NULL AND cr.name != '' THEN cr.name ELSE 'Unknown Item' END AS 'Order Lines/Product'
    , pi.qty AS 'Order Lines/Quantity', pi.qty AS 'Order Lines/Received Qty', pi.price AS 'Order Lines/Unit Price'
    from purchase p
    left join purchase_item pi on pi.id = p.id
    left join code_relations cr on cr.code = pi.code
    left join card c1 on c1.id = p.staff_id
    left join card c2 on c2.id = p.supplier_id
    --where p.date_create >= DATEADD(YEAR, -6, GETDATE());
    where p.date_create > '2025-05-12'
    """
    # 可以添加这个语句，设置1年 where p.date_create >= DATEADD(YEAR, -1, GETDATE());
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条采购记录")
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def convert_to_odoo_format(df):
    """将数据转换为Odoo采购订单导入格式"""
    
    # 添加Odoo需要的额外字段并放在前列
    df.insert(0, 'Company', 'Bright Home Electrical Apparatus Ltd')
    df.insert(1, 'Priority', 'Normal')
    df.insert(2, 'Status', 'Purchase Order')
    
    # 格式化日期列为仅日期格式（YYYY-MM-DD）
    if 'Order Deadline' in df.columns:
        df['Order Deadline'] = pd.to_datetime(df['Order Deadline']).dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # 更正Buyer字段中的拼写错误
    if 'Buyer' in df.columns:
        df['Buyer'] = df['Buyer'].str.replace('Daivd Wang', 'David Wang')
        df['Buyer'] = df['Buyer'].str.replace('Support', 'Administrator')
    
    # 为po_number添加P前缀
    if 'Order Reference' in df.columns:
        df['Order Reference'] = 'P0' + df['Order Reference'].astype(str)

    # 重命名列以匹配Odoo模板
    column_mapping = {
        'Vendor': 'Vendor',
        'Order Deadline': 'Order Deadline',
        'Buyer': 'Buyer',
        'Source Document': 'Source Document',
        'Total': 'Total',
        'Order Lines/Product': 'Order Lines/Product',
        'Order Lines/Description': 'Order Lines/Description',
        'Order Lines/Quantity': 'Order Lines/Quantity',
        'Order Lines/Unit Price': 'Order Lines/Unit Price',
    }
    
    df = df.rename(columns=column_mapping)
    
    # 分组处理相同Order Reference的记录
    if 'Order Reference' in df.columns:
        # 定义需要保留的字段
        keep_cols = ['Order Lines/Product', 'Order Lines/Description', 'Order Lines/Quantity', 'Order Lines/Received Qty', 'Order Lines/Unit Price']
        
        # 分组处理
        grouped = df.groupby('Order Reference')
        
        # 对每组处理：第一条记录保留全部字段，后续记录只保留指定字段
        def process_group(group):
            if len(group) > 1:
                first_row = group.iloc[0:1].copy()
                other_rows = group.iloc[1:].copy()
                
                # 清除非保留字段
                for col in other_rows.columns:
                    if col not in keep_cols:
                        other_rows[col] = ''
                
                return pd.concat([first_row, other_rows])
            return group
        
        df = grouped.apply(process_group).reset_index(drop=True)
    
    return df

def save_to_file(df, filename='purchase_order_template'):
    """将数据保存为CSV和Excel文件"""
    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)
        
        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {backups_dir}")
        
        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件到backups文件夹: {src} -> {dst}")
        
        # CSV文件路径
        csv_path = os.path.join(script_dir, f"{filename}_{timestamp}.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"数据已成功保存为CSV文件: {csv_path}")
        
        # Excel文件路径
        excel_path = os.path.join(script_dir, f"{filename}_{timestamp}.xlsx")
        df.to_excel(excel_path, index=False)
        print(f"数据已成功保存为Excel文件: {excel_path}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 1. 连接数据库
    engine = connect_to_sql_server()
    if engine:
        # 2. 获取数据
        df = fetch_purchase_data(engine)
        if df is not None:
            # 3. 转换格式
            odoo_df = convert_to_odoo_format(df)
            # 4. 保存为CSV和Excel
            save_to_file(odoo_df)
        engine.dispose()