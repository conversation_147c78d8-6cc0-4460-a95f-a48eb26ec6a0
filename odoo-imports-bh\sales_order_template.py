import pyodbc
import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine

#check test2	1 of 	4086
#check qty = 0	2 of	5084
#Unknown Item	3 of	5715
#是否有翻页错误	4 of	6045
#	5 of	5903
#	6 of	5539
#	7 of	5684
#	8 of	5776
#	9 of	6404
#	10 of	6463
#	11 of	6531
#	12 of	6610
#	13 of	6593
#	14 of	6319
#	15 of	4945
#	total	87697
# 数据库连接配置
# python310 odoo-imports-bh/sales_order_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
SQL_SERVER_CONFIG = {
    'server': '192.168.10.214',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def fetch_sales_data(conn):
    """从SQL Server获取销售订单数据"""
    query = """
    SET DATEFORMAT ymd;
    SELECT
        ISNULL(NULLIF(RTRIM(LTRIM(c3.trading_name)), ''), 'Unknown Customer') AS 'Customer',
        o.record_date AS 'Order Date',
        'As soon as possible' AS 'Shipping Policy',
        c3.price_level AS Pricelist,
        c1.name AS Salesperson,
        o.po_number AS 'Customer Reference',
        o.id AS 'Order Reference',
        CASE
            WHEN oi.item_name IS NOT NULL AND oi.item_name != '' THEN oi.item_name
            ELSE cr.name
        END AS 'Order Lines/Description',
        ISNULL(NULLIF(cr.name, ''), 'Unknown Item') AS 'Order Lines/Product',
        oi.quantity AS 'Order Lines/Quantity',
        oi.commit_price AS 'Order Lines/Unit Price',
        oi.discount_percent AS 'Order Lines/Discount'
    from orders o
    left join order_item oi on o.id = oi.id
    left join code_relations cr on cr.code = oi.code
    left join card c1 on c1.id = o.sales
    left join card c2 on c2.id = o.sales_manager
    left join card c3 on c3.id = o.card_id and c3.type = 2 AND c3.id <> 0
    where o.record_date > '2025-05-11'
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条销售订单记录")
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def convert_to_odoo_format(df):
    """将数据转换为Odoo销售订单导入格式"""

    # 格式化日期列为仅日期格式（YYYY-MM-DD）
    if 'Order Date' in df.columns:
        df['Order Date'] = pd.to_datetime(df['Order Date']).dt.strftime('%Y-%m-%d')

    # 转换Pricelist格式并添加Level列
    if 'Pricelist' in df.columns:
        # 先填充NaN值为1，然后转换为整数
        df['Pricelist'] = df['Pricelist'].fillna(1)
        df['Level'] = 'Level ' + df['Pricelist'].astype(int).astype(str)
        df['Pricelist'] = 'Level Price ' + df['Pricelist'].astype(int).astype(str)

    # 添加tax列，默认值15%
    df['Order Lines / Taxes'] = '15%'

    # 为po_number添加P前缀
    if 'Order Reference' in df.columns:
        df['Order Reference'] = 'S' + df['Order Reference'].astype(str)

    # 重命名列以匹配Odoo模板
    column_mapping = {
        'Customer': 'Customer',
        'Order Date': 'Order Date',
        'Shipping Policy': 'Shipping Policy',
        'Pricelist': 'Pricelist',
        'Level': 'Level',
        'Salesperson': 'Salesperson',
        'Customer Reference': 'Customer Reference',
        'Order Reference': 'Order Reference',
        'Order Lines/Product': 'Order Lines/Product',
        'Order Lines/Description': 'Order Lines/Description',
        'Order Lines/Quantity': 'Order Lines/Quantity',
        'Order Lines/Unit Price': 'Order Lines/Unit Price',
        'Order Lines/Discount': 'Order Lines/Discount',
        'Order Lines/Taxes': 'Order Lines/Taxes',
    }

    df = df.rename(columns=column_mapping)

    # 分组处理相同Order Reference的记录
    if 'Order Reference' in df.columns:
        # 定义需要保留的字段
        keep_cols = ['Order Lines/Product', 'Order Lines/Description', 'Order Lines/Quantity', 'Order Lines/Unit Price', 'Order Lines/Discount', 'Order Lines/Taxes']

        # 分组处理
        grouped = df.groupby('Order Reference')

        # 对每组处理：第一条记录保留全部字段，后续记录只保留指定字段
        def process_group(group):
            if len(group) > 1:
                first_row = group.iloc[0:1].copy()
                other_rows = group.iloc[1:].copy()

                # 清除非保留字段
                for col in other_rows.columns:
                    if col not in keep_cols:
                        other_rows[col] = ''

                return pd.concat([first_row, other_rows])
            return group

        df = grouped.apply(process_group, include_groups=True).reset_index(drop=True)

    return df

def save_to_file(df, filename='sales_order_template'):
    """将数据保存为CSV和Excel文件，按20000条记录拆分到不同文件"""
    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)

        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {backups_dir}")

        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件到backups文件夹: {src} -> {dst}")

        # 按20000条记录拆分数据
        total_rows = len(df)
        file_count = (total_rows + 19999) // 20000  # 向上取整

        for i in range(file_count):
            start_idx = i * 20000
            end_idx = min((i + 1) * 20000, total_rows)
            chunk_df = df.iloc[start_idx:end_idx]

            # 生成文件名
            file_suffix = f"_{i+1}_of_{file_count}_{timestamp}"

            # 保存CSV文件
            csv_path = os.path.join(script_dir, f"{filename}{file_suffix}.csv")
            chunk_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"已保存CSV文件: {csv_path}，包含{len(chunk_df)}行数据")

            # 保存Excel文件
            excel_path = os.path.join(script_dir, f"{filename}{file_suffix}.xlsx")
            chunk_df.to_excel(excel_path, index=False)
            print(f"已保存Excel文件: {excel_path}，包含{len(chunk_df)}行数据")

    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 1. 连接数据库
    engine = connect_to_sql_server()
    if engine:
        # 2. 获取数据
        df = fetch_sales_data(engine)
        if df is not None:
            # 3. 转换格式
            odoo_df = convert_to_odoo_format(df)
            # 4. 保存为Excel
            save_to_file(odoo_df)
        engine.dispose()