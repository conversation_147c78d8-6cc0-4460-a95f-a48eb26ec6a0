import pyodbc
import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine

# 数据库连接配置
# python310 odoo-imports-bh/sales_invoice_with_payment_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
# payment_method: cash, eftpos, credit apply, cheque, credit card, bank card, deposit, others
# 拆分文件，根据TYPE判断是否为credit note
# 检查是否还有负数，在payment_json中
SQL_SERVER_CONFIG = {
    'server': '192.168.10.214',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def fetch_invoice_data(conn):
    """从SQL Server获取销售发票及付款数据，每行代表一个付款或一个无付款的发票行"""
    query = """
    SET DATEFORMAT ymd;
    WITH PaymentTotals AS (
        SELECT
            ti.invoice_number,
            SUM(ti.amount_applied) as total_payment_amount
        FROM tran_invoice ti
        WHERE ti.amount_applied <> 0
        GROUP BY ti.invoice_number
    )
    SELECT
        i.invoice_number AS 'INVOICE ID',
        ISNULL(NULLIF(RTRIM(LTRIM(c3.trading_name)), ''), 'Unknown Customer') AS 'PARTNER',
        'NZD' AS 'CURRENCY',
        ISNULL(NULLIF(cr.name, ''), 'Unknown Item') AS 'PRODUCT',
        '41110' AS 'ACCOUNT',
        s.quantity AS 'QUANTITY',
        'Units' AS 'UOM',
        s.name AS 'DESCRIPTION',
        s.commit_price AS 'PRICE',
        ISNULL(NULLIF(c1.name, ''), 'Administrator') AS 'SALESPERSON',
        '15%' AS 'TAX',
        i.commit_date AS 'DATE',
        s.discount_percent AS 'DISCOUNT',
        CASE
            WHEN ISNULL(pt.total_payment_amount, 0) < 0 THEN 'credit note'
            ELSE 'customer invoice'
        END as 'TYPE',
        STUFF((
            SELECT ',' + CONCAT(
                '{"journal": "',
                CASE
                    WHEN e.name IN ('cash', '') THEN 'Cash'
                    WHEN e.name = 'eftpos' THEN 'EFTPOS'
                    WHEN e.name IN ('credit apply', 'cheque', 'credit card', 'bank card', 'deposit', 'others') THEN 'BNZ 02-0176-0067997-66'
                    ELSE e.name
                END,
                '", "amount": ', ti.amount_applied,
                ', "payment_date": "', FORMAT(td.trans_date, 'yyyy-MM-dd'),
                '"}'
            )
            FROM tran_invoice ti
            LEFT JOIN tran_detail td ON td.id = ti.tran_id
            LEFT JOIN enum e ON e.class = 'payment_method' AND e.id = td.payment_method
            WHERE ti.invoice_number = i.invoice_number AND ti.amount_applied <> 0
            FOR XML PATH('')
        ), 1, 1, '') AS 'PAYMENT_JSON'
    FROM invoice i
    LEFT JOIN orders o ON o.invoice_number = i.invoice_number
    LEFT JOIN sales s ON s.invoice_number = i.invoice_number
    LEFT JOIN code_relations cr ON cr.code = s.code
    LEFT JOIN card c1 ON c1.id = o.sales
    LEFT JOIN card c3 ON c3.id = o.card_id AND c3.type = 2 AND c3.id <> 0
    LEFT JOIN PaymentTotals pt ON pt.invoice_number = i.invoice_number
    WHERE i.invoice_number <> 0
      --AND i.commit_date >= DATEADD(YEAR, -1, GETDATE())
      AND i.commit_date > '2025-05-11'
      AND s.commit_price IS NOT NULL
      AND s.quantity IS NOT NULL
    ORDER BY i.invoice_number, s.id;
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条记录 (包含发票行和付款信息)")
        # Fill missing payment info with appropriate defaults if needed
        df['PAYMENT_JSON'] = df['PAYMENT_JSON'].fillna('')
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def convert_to_odoo_format(df):
    """将数据转换为Odoo销售发票及付款导入格式"""

    # 格式化日期列
    df['DATE'] = pd.to_datetime(df['DATE'], errors='coerce')
    df['DATE'] = df['DATE'].dt.strftime('%Y-%m-%d').replace('NaT', '')

    # 处理贷项通知单的数量
    credit_note_mask = df['TYPE'] == 'credit note'
    df.loc[credit_note_mask, 'QUANTITY'] = -df.loc[credit_note_mask, 'QUANTITY']

    # 处理 PAYMENT_JSON，只保留每个发票第一行的付款信息
    # 创建一个掩码，标记每个发票的第一行
    first_rows = df.groupby('INVOICE ID').cumcount() == 0

    # 检查PAYMENT_JSON中是否包含负数金额并计算总金额
    def process_payment_json(payment_json, is_credit_note):
        if not payment_json or pd.isna(payment_json):
            return 'no', 0, ''
        try:
            # 移除首尾的方括号并分割多个支付记录
            payments = payment_json.strip('[]').split('},')
            has_negative = False
            total_amount = 0
            processed_payments = []

            for payment in payments:
                # 确保每个支付记录都有正确的格式
                if not payment.endswith('}'):
                    payment += '}'
                # 提取amount值
                amount_str = payment.split('"amount":')[1].split(',')[0].strip()
                amount = float(amount_str)

                # 如果是贷项通知单，反转金额
                if is_credit_note:
                    amount = -amount

                total_amount += amount
                if amount < 0:
                    has_negative = True

                # 更新payment中的amount
                payment = payment.replace(f'"amount": {amount_str}', f'"amount": {amount}')
                processed_payments.append(payment)

            # 重新组合处理后的payments
            processed_json = ','.join(processed_payments)
            return 'yes' if has_negative else 'no', total_amount, processed_json
        except Exception as e:
            print(f"处理付款JSON时出错: {e}, JSON: {payment_json}")
            return 'no', 0, ''

    # 将非第一行的 PAYMENT_JSON 设置为空字符串
    df.loc[~first_rows, 'PAYMENT_JSON'] = ''

    # 处理第一行的付款JSON
    payment_results = df.loc[first_rows].apply(
        lambda row: process_payment_json(row['PAYMENT_JSON'], row['TYPE'] == 'credit note'),
        axis=1
    )

    # 分别处理返回的三个值
    df.loc[first_rows, 'HAS_NEGATIVE_PAYMENT'] = payment_results.apply(lambda x: x[0])
    df.loc[first_rows, 'TOTAL_PAYMENT_AMOUNT'] = payment_results.apply(lambda x: x[1])
    df.loc[first_rows, 'PAYMENT_JSON'] = payment_results.apply(lambda x: f'[{x[2]}]' if x[2] else '')

    # 计算每个发票的总金额
    invoice_totals = df.groupby('INVOICE ID').apply(lambda x: (x['QUANTITY'] * x['PRICE']).sum())
    df.loc[first_rows, 'TOTAL_INVOICE_AMOUNT'] = df.loc[first_rows, 'INVOICE ID'].map(invoice_totals)

    # 将非第一行的相关列设置为空字符串或0
    df.loc[~first_rows, 'HAS_NEGATIVE_PAYMENT'] = ''
    df.loc[~first_rows, 'TOTAL_PAYMENT_AMOUNT'] = ''
    df.loc[~first_rows, 'TOTAL_INVOICE_AMOUNT'] = ''

    # 列名映射，严格按照用户要求的标题
    column_mapping = {
        'INVOICE ID': 'INVOICE ID',
        'PARTNER': 'PARTNER',
        'CURRENCY': 'CURRENCY',
        'PRODUCT': 'PRODUCT',
        'ACCOUNT': 'ACCOUNT',
        'QUANTITY': 'QUANTITY',
        'UOM': 'UOM',
        'DESCRIPTION': 'DESCRIPTION',
        'PRICE': 'PRICE',
        'SALESPERSON': 'SALESPERSON',
        'TAX': 'TAX',
        'DATE': 'DATE',
        'DISCOUNT': 'DISCOUNT',
        'PAYMENT_JSON': 'PAYMENT_JSON',
        'TYPE': 'TYPE',
        'HAS_NEGATIVE_PAYMENT': 'HAS_NEGATIVE_PAYMENT',
        'TOTAL_PAYMENT_AMOUNT': 'TOTAL_PAYMENT_AMOUNT',
        'TOTAL_INVOICE_AMOUNT': 'TOTAL_INVOICE_AMOUNT'
    }

    # 重命名列
    df_odoo = df.rename(columns=column_mapping)

    # 清理其他列的 NaN/NaT 为空字符串
    for col in df_odoo.columns:
        if col not in ['QUANTITY', 'PRICE', 'DISCOUNT', 'TOTAL_PAYMENT_AMOUNT']:  # 数值列除外
            if df_odoo[col].dtype == 'object':
                df_odoo[col] = df_odoo[col].fillna('')

    # 添加 'INV' 前缀到 INVOICE ID
    df_odoo['INVOICE ID'] = 'INV' + df_odoo['INVOICE ID'].astype(str)

    # 调整列顺序
    desired_order = [
        'INVOICE ID', 'PARTNER', 'CURRENCY', 'PRODUCT', 'ACCOUNT', 'QUANTITY', 'UOM',
        'DESCRIPTION', 'PRICE', 'SALESPERSON', 'TAX', 'DATE', 'DISCOUNT',
        'PAYMENT_JSON', 'TYPE', 'HAS_NEGATIVE_PAYMENT', 'TOTAL_PAYMENT_AMOUNT', 'TOTAL_INVOICE_AMOUNT'
    ]
    df_odoo = df_odoo.reindex(columns=desired_order, fill_value='')

    return df_odoo

def save_to_file(df, filename='sales_invoice_with_payment_template'):
    """将数据保存为CSV和Excel文件，按20000条记录拆分到不同文件，并根据TYPE判断是否为credit note"""
    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)

        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {os.path.basename(backups_dir)}")

        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件 {os.path.basename(src)} 到 backups 文件夹")

        # 根据TYPE将数据分为普通发票和贷项通知单两组
        invoice_df = df[df['TYPE'] == 'customer invoice'].copy()
        credit_note_df = df[df['TYPE'] == 'credit note'].copy()

        # 处理普通发票数据
        if not invoice_df.empty:
            total_rows = len(invoice_df)
            file_count = (total_rows + 19999) // 20000  # 向上取整

            for i in range(file_count):
                start_idx = i * 20000
                end_idx = min((i + 1) * 20000, total_rows)
                chunk_df = invoice_df.iloc[start_idx:end_idx]

                # 生成文件名
                file_suffix = f"_{i+1}_of_{file_count}_{timestamp}"

                # 保存CSV文件
                csv_path = os.path.join(script_dir, f"{filename}{file_suffix}.csv")
                chunk_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                print(f"已保存普通发票CSV文件: {os.path.basename(csv_path)}，包含{len(chunk_df)}行数据")

                # 保存Excel文件
                excel_path = os.path.join(script_dir, f"{filename}{file_suffix}.xlsx")
                chunk_df.to_excel(excel_path, index=False, engine='openpyxl')
                print(f"已保存普通发票Excel文件: {os.path.basename(excel_path)}，包含{len(chunk_df)}行数据")

        # 处理贷项通知单数据
        if not credit_note_df.empty:
            total_rows = len(credit_note_df)
            file_count = (total_rows + 19999) // 20000  # 向上取整

            for i in range(file_count):
                start_idx = i * 20000
                end_idx = min((i + 1) * 20000, total_rows)
                chunk_df = credit_note_df.iloc[start_idx:end_idx]

                # 生成文件名
                file_suffix = f"_credit_note_{i+1}_of_{file_count}_{timestamp}"

                # 保存CSV文件
                csv_path = os.path.join(script_dir, f"{filename}{file_suffix}.csv")
                chunk_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                print(f"已保存贷项通知单CSV文件: {os.path.basename(csv_path)}，包含{len(chunk_df)}行数据")

                # 保存Excel文件
                excel_path = os.path.join(script_dir, f"{filename}{file_suffix}.xlsx")
                chunk_df.to_excel(excel_path, index=False, engine='openpyxl')
                print(f"已保存贷项通知单Excel文件: {os.path.basename(excel_path)}，包含{len(chunk_df)}行数据")

    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 1. 连接数据库
    engine = connect_to_sql_server()
    if engine:
        # 2. 获取数据
        df = fetch_invoice_data(engine)
        if df is not None:
            # 3. 转换格式
            odoo_df = convert_to_odoo_format(df)
            # 4. 保存为Excel
            save_to_file(odoo_df, filename='sales_invoice_with_payment_template')
        engine.dispose()