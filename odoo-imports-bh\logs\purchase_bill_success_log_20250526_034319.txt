[2025-05-26 03:43:19] 成功更新采购订单 P07351 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07351'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07359 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07359'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07376 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07376'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07379 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07379'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07387 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07387'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07394 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07394'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07399 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07399'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07401 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07401'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07407 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07407'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07409 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07409'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07410 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07410'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07412 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07412'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07419 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07419'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07422 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07422'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07428 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07428'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07434 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07434'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07441 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07441'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07444 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07444'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07446 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07446'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07448 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07448'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07450 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07450'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07451 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07451'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07452 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07452'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07455 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07455'
    

[2025-05-26 03:43:19] 成功更新采购订单 P07460 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07460'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07461 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07461'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07462 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07462'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07464 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07464'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07469 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07469'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07470 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07470'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07471 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07471'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07477 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07477'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07478 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07478'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07479 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07479'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07483 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07483'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07486 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07486'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07489 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07489'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07490 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07490'
    

[2025-05-26 03:43:20] 成功更新采购订单 P07513 的状态
执行的SQL: 
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = 'P07513'
    


总结统计信息:
成功更新状态的采购订单数量: 39
更新状态失败的采购订单数量: 0
