<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="account_move_form_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">account.move.form.inherit.l10n_gr_edi</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">

            <header position="inside">
                <button name="l10n_gr_edi_try_send_expense_classification"
                        type="object"
                        string="Send Expense Classifications to MyDATA"
                        invisible="not l10n_gr_edi_enable_send_expense_classification"/>
            </header>

            <header position="after">
                <div class="m-0" role="alert" invisible="not (l10n_gr_edi_enable_send_invoices and l10n_gr_edi_alerts)">
                    <field name="l10n_gr_edi_alerts" class="o_field_html" widget="actionable_errors"/>
                </div>
            </header>

            <xpath expr="//group[@id='header_right_group']" position="inside">
                <field name="l10n_gr_edi_available_inv_type" invisible="1"/>
                <field name="l10n_gr_edi_inv_type"
                       widget="dynamic_selection"
                       options="{'available_field': 'l10n_gr_edi_available_inv_type'}"
                       invisible="not l10n_gr_edi_enable_view_mydata"
                       required="1"
                       readonly="state != 'draft'"/>
                <field name="l10n_gr_edi_correlation_id"
                       invisible="not (l10n_gr_edi_enable_view_mydata and l10n_gr_edi_need_correlated)"
                       required="l10n_gr_edi_need_correlated"
                       readonly="state != 'draft'"/>
                <field name="l10n_gr_edi_payment_method"
                       invisible="not (l10n_gr_edi_enable_view_mydata and l10n_gr_edi_need_payment_method)"
                       required="l10n_gr_edi_need_payment_method"
                       readonly="state != 'draft'"/>
                <!--The MyDATA Mark field will have the following behaviors:
                    * only appear in greece invoices/bills
                    * always visible on bills, and only visible on invoices when filled
                    * editable only on draft bills -->
                <field name="l10n_gr_edi_mark"
                       invisible="not (l10n_gr_edi_enable_view_mydata and (l10n_gr_edi_mark or move_type in ('in_invoice', 'in_refund', 'in_receipt')))"
                       readonly="not (state == 'draft' and move_type in ('in_invoice', 'in_refund'))"/>
                <field name="l10n_gr_edi_cls_mark"
                       invisible="not (l10n_gr_edi_enable_view_mydata and l10n_gr_edi_cls_mark)"
                       readonly="1"/>
                <field name="l10n_gr_edi_state"
                       invisible="not (l10n_gr_edi_enable_view_mydata and l10n_gr_edi_state)"
                       readonly="1"/>
            </xpath>

            <xpath expr="//notebook/page[@name='invoice_tab']//list/field[@name='name']" position="after">
                <field name="l10n_gr_edi_available_cls_category" column_invisible="1"/>
                <field name="l10n_gr_edi_available_cls_type" column_invisible="1"/>
                <field name="l10n_gr_edi_available_cls_vat" column_invisible="1"/>
                <field name="l10n_gr_edi_detail_type" optional="hide" column_invisible="parent.country_code != 'GR'"/>
                <field name="l10n_gr_edi_cls_category"
                       widget="dynamic_selection"
                       options="{'available_field': 'l10n_gr_edi_available_cls_category'}"
                       optional="show"
                       column_invisible="parent.country_code != 'GR'"/>
                <field name="l10n_gr_edi_cls_type"
                       widget="dynamic_selection"
                       options="{'available_field': 'l10n_gr_edi_available_cls_type'}"
                       optional="show"
                       column_invisible="parent.country_code != 'GR'"/>
                <field name="l10n_gr_edi_cls_vat"
                       widget="dynamic_selection"
                       options="{'available_field': 'l10n_gr_edi_available_cls_vat'}"
                       optional="hide"
                       column_invisible="parent.country_code != 'GR'"/>
            </xpath>

            <xpath expr="//notebook/page[@name='invoice_tab']//list/field[@name='tax_ids']" position="after">
                <field name="l10n_gr_edi_need_exemption_category" column_invisible="True"/>
                <field name="l10n_gr_edi_tax_exemption_category"
                       optional="hide"
                       column_invisible="parent.country_code != 'GR'"
                       readonly="not l10n_gr_edi_need_exemption_category"/>
            </xpath>

            <!-- MyDATA Documents Tab -->
            <xpath expr="//page[@id='other_tab_entry']" position="after">
                <page id="l10n_gr_edi_documents"
                      string="MyDATA"
                      invisible="not l10n_gr_edi_document_ids">
                    <field name="l10n_gr_edi_document_ids">
                        <list create="false" delete="false" edit="false" no_open="1"
                              decoration-danger="state in ('invoice_error', 'bill_error')"
                              decoration-success="state in ('invoice_sent', 'bill_sent')">
                            <field name="message" column_invisible="1"/>
                            <field name="attachment_id" column_invisible="1"/>
                            <field name="datetime"/>
                            <field name="state" widget="account_document_state"/>
                            <button name="action_download"
                                    type="object"
                                    string="Download"
                                    invisible="not attachment_id"/>
                        </list>
                    </field>
                </page>
            </xpath>

        </field>
    </record>

    <record id="view_out_invoice_tree_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">account.out.invoice.list.inherit.l10n_gr_edi</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_out_invoice_tree"/>
        <field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="l10n_gr_edi_state" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_out_credit_note_tree_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">account.out.credit.note.list.inherit.l10n_gr_edi</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_out_credit_note_tree"/>
        <field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="l10n_gr_edi_state" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_in_invoice_bill_tree_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">account.in.invoice.list.inherit.l10n_gr_edi</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_in_invoice_tree"/>
        <field name="arch" type="xml">
            <field name="status_in_payment" position="before">
                <field name="l10n_gr_edi_state" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="view_account_invoice_filter_inherit_l10n_gr_edi" model="ir.ui.view">
        <field name="name">account.invoice.select.inherit.l10n_gr_edi</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//search/field[@name='journal_id']" position="after">
                <field name="l10n_gr_edi_state"/>
            </xpath>
            <xpath expr="//filter[@name='to_check']" position="after">
                <filter string="To send to MyDATA" name="l10n_gr_edi_state_false"
                        domain="[('l10n_gr_edi_state', 'in', (False, 'bill_fetched'))]"/>
                <filter string="Sent to MyDATA" name="l10n_gr_edi_state_sent"
                        domain="[('l10n_gr_edi_state', '=', 'invoice_sent')]"/>
            </xpath>
            <xpath expr="//group" position="inside">
                <filter string="MyDATA Status" name="l10n_gr_edi_state_group"
                        domain="" context="{'group_by':'l10n_gr_edi_state'}"/>
            </xpath>
        </field>
    </record>

    <record id="l10n_gr_edi_action_try_send_batch" model="ir.actions.server">
        <field name="name">Send to MyDATA</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                records._l10n_gr_edi_try_send_batch()
        </field>
    </record>

</odoo>
