import pyodbc
import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine

# 数据库连接配置
# python310 odoo-imports-bh/stock_inventory_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
SQL_SERVER_CONFIG = {
    'server': '192.168.10.214',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def fetch_inventory_data(conn):
    """从SQL Server获取库存数据"""
    query = """
    select
        CASE
            WHEN sq.branch_id = 1 THEN 'C-WH/Stock'
            WHEN sq.branch_id = 2 THEN 'ET-WH/Stock'
            WHEN sq.branch_id = 8 THEN 'NS-WH/Stock'
        END AS Location,
        cr.name AS Product,
        sq.qty AS 'Counted Quantity'
    from stock_qty sq
    left join code_relations cr on cr.code = sq.code
    where branch_id in (1,2,8) and sq.code > 1019 and name != '' and name is not null
    and sq.code <> 1128 and sq.code <> 1130 and sq.code <> 4237
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条库存记录")
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def convert_to_odoo_format(df):
    """将数据转换为Odoo库存盘点导入格式"""

    # 定义需要删除的产品名称列表
    products_to_exclude = [
        'JD02-W Step light silver frame ,cool white',
        'JD02-B Step light silver frame ,blue light',
        'JD05-Y Step light white frame, warm white light',
        'RG6 300m; cat5e 305m; alarm cable 100m',
        'JD05-W Step light white frame, cool white light',
        'E27 9w energy saver lamp warmwhite 3000K',
        'JD02-W Silver frame round modern LED Recessed',
        'JD02-Y Silver frame round modern LED Recessed ste',
        'JD05-W Silver frame round modern LED Recessed step',
        'JD05-Y Silver frame round modern LED Recessed ste',
        'JD08-N  Silver frame round modern LED Recessed step light/ multi-color temperature available'
    ]

    # 过滤掉指定的产品名称
    initial_count = len(df)
    df = df[~df['Product'].isin(products_to_exclude)]
    excluded_count = initial_count - len(df)
    if excluded_count > 0:
        print(f"已过滤掉 {excluded_count} 条指定产品记录")

    # 添加当前日期作为计划日期
    df['Scheduled Date'] = datetime.datetime.now().strftime('%Y-%m-%d')

    # 如果Counted Quantity小于0，则设置为0
    df['Counted Quantity'] = df['Counted Quantity'].apply(lambda x: 0 if x < 0 else x)

    # 选择并重排列
    columns = [
        'Location',
        'Product',
        'Counted Quantity',
        'Scheduled Date'
    ]

    df = df[columns]
    return df

def save_to_file(df, filename='stock_inventory_import'):
    """将数据保存为CSV文件和Excel文件（按分支分表）"""
    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)

        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {backups_dir}")

        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件到backups文件夹: {src} -> {dst}")

        # CSV文件路径
        csv_path = os.path.join(script_dir, f"{filename}_{timestamp}.csv")

        # 保存为CSV文件
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"数据已成功保存为CSV文件: {csv_path}")

        # Excel文件路径
        excel_path = os.path.join(script_dir, f"{filename}_{timestamp}.xlsx")

        # 按Location分组并保存到不同的sheet
        with pd.ExcelWriter(excel_path) as writer:
            # 获取所有分支
            branches = df['Location'].unique()

            # 保存全部数据到All sheet
            df.to_excel(writer, sheet_name='All', index=False)

            # 为每个分支创建单独的sheet
            for branch in branches:
                # 清理sheet名称，移除无效字符
                sheet_name = branch.replace('/', '-').replace('\\', '-').replace('?', '').replace('*', '')
                sheet_name = sheet_name.replace('[', '(').replace(']', ')').replace(':', '-')
                # 确保sheet名称不超过31个字符（Excel限制）
                if len(sheet_name) > 31:
                    sheet_name = sheet_name[:31]

                branch_df = df[df['Location'] == branch]
                branch_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"数据已成功保存为Excel文件（按分支分表）: {excel_path}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 1. 连接数据库
    engine = connect_to_sql_server()
    if engine:
        # 2. 获取数据
        df = fetch_inventory_data(engine)
        if df is not None:
            # 3. 转换格式
            odoo_df = convert_to_odoo_format(df)
            # 4. 保存为Excel
            save_to_file(odoo_df)
        engine.dispose()