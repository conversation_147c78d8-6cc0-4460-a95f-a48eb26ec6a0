import pandas as pd
import os
import shutil
import datetime
from sqlalchemy import create_engine
import psycopg2

# 配置开关：是否启用更新采购订单状态
ENABLE_STATUS_UPDATE = False  # 设置为 True 启用，设置为 False 禁用

# 配置开关：是否启用文件导出
ENABLE_FILE_EXPORT = False  # 设置为 True 启用，设置为 False 禁用

# 配置开关：是否关联bill和purchase order
ENABLE_BILL_PO_LINKING = True # 设置为 True 启用，设置为 False 禁用

# 数据库连接配置
# test2，des为空的
# python310 odoo-imports-bh/purchase_bill_template.py
# dev_brighthomenew20250501002256
# dev_brighthomenew20250512002626
SQL_SERVER_CONFIG = {
    'server': '**************',
    'database': 'brighthomenew',
    'username': 'eznz',
    'password': '9seqxtf7',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

# PostgreSQL 数据库连接配置 (从 main 函数移到全局)
PG_CONFIG = {
    'dbname': "brighthome_odoo18_live",
    'user': "bhsa",
    'password': "Bh44Patiki@Akl",
    'host': "bh-postgresql-server.postgres.database.azure.com",
    'port': "5432"
}

def connect_to_sql_server():
    """连接SQL Server数据库"""
    try:
        driver = SQL_SERVER_CONFIG['driver'].replace('{', '').replace('}', '')
        conn_str = f"mssql+pyodbc://{SQL_SERVER_CONFIG['username']}:{SQL_SERVER_CONFIG['password']}@{SQL_SERVER_CONFIG['server']}/{SQL_SERVER_CONFIG['database']}?driver={driver}"
        engine = create_engine(conn_str)
        print("成功连接到SQL Server数据库")
        return engine
    except Exception as e:
        print(f"连接数据库时出错: {e}")
        return None

def connect_to_postgresql():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        print("成功连接到PostgreSQL数据库")
        return conn
    except Exception as e:
        print(f"连接PostgreSQL数据库时出错: {e}")
        return None

def fetch_purchase_data(conn):
    """从SQL Server获取采购账单数据"""
    query = """
    SET DATEFORMAT ymd;
    SELECT RTRIM(LTRIM(c2.trading_name)) AS 'Partner', p.date_invoiced AS 'Invoice/Bill Date', p.date_invoiced AS 'Due Date'
    , c1.name AS Salesperson, p.po_number AS 'Source', p.po_number AS 'Number', p.inv_number AS 'Reference'
    , CASE WHEN cr.name IS NOT NULL AND cr.name != '' THEN cr.name ELSE 'Unknown Item' END AS 'Invoice Lines/Product'
    , pi.qty AS 'Invoice Lines/Quantity', pi.price AS 'Invoice Lines/Unit Price'
    from purchase p
    left join purchase_item pi on pi.id = p.id
    left join code_relations cr on cr.code = pi.code
    left join card c1 on c1.id = p.staff_id
    left join card c2 on c2.id = p.supplier_id
    where p.date_invoiced is not null
    --and p.po_number = 7467 -- Changed 'P07467' to 7467 to match integer type
    --and p.date_invoiced >= DATEADD(YEAR, -5, GETDATE());
    and p.date_invoiced > '2025-05-01'
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"成功获取{len(df)}条采购账单记录")
        return df
    except Exception as e:
        print(f"查询数据时出错: {e}")
        return None

def convert_to_odoo_format(df, pg_conn=None):
    """将数据转换为Odoo采购账单导入格式"""

    # 格式化日期列为仅日期格式（YYYY-MM-DD）
    if 'Invoice/Bill Date' in df.columns:
        df['Invoice/Bill Date'] = pd.to_datetime(df['Invoice/Bill Date']).dt.strftime('%Y-%m-%d')
    if 'Due Date' in df.columns:
        df['Due Date'] = pd.to_datetime(df['Due Date']).dt.strftime('%Y-%m-%d')

    # 为po_number添加P前缀
    if 'Source' in df.columns:
        df['Source'] = 'P0' + df['Source'].astype(str)
        df['Number'] = 'B0' + df['Number'].astype(str)

    # 重命名列以匹配Odoo模板
    column_mapping = {
        'Partner': 'Partner',
        'Invoice/Bill Date': 'Invoice/Bill Date',
        'Due Date': 'Due Date',
        'Salesperson': 'Salesperson',
        'Source': 'Source',
        'Number': 'Number',
        'Reference': 'Reference',
        'Invoice Lines/Product': 'Invoice Lines/Product',
        'Invoice Lines/Quantity': 'Invoice Lines/Quantity',
        'Invoice Lines/Unit Price': 'Invoice Lines/Unit Price',
    }

    df = df.rename(columns=column_mapping)

    # 分组处理相同Source的记录
    if 'Source' in df.columns:
        # 定义需要保留的字段
        keep_cols = ['Invoice Lines/Product', 'Invoice Lines/Quantity', 'Invoice Lines/Unit Price']

        # 分组处理
        grouped = df.groupby('Source')

        # 设置日志文件路径
        script_dir = os.path.dirname(__file__)
        logs_dir = os.path.join(script_dir, "logs")
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        success_log_file = os.path.join(logs_dir, f"purchase_bill_success_log_{timestamp}.txt")
        error_log_file = os.path.join(logs_dir, f"purchase_bill_error_log_{timestamp}.txt")

        # 初始化统计计数器
        success_count_status = 0
        error_count_status = 0
        success_count_link = 0
        error_count_link = 0

        # 对每组处理：第一条记录保留全部字段，后续记录只保留指定字段
        def process_group(group):
            nonlocal success_count_status, error_count_status, success_count_link, error_count_link
            first_row = group.iloc[0:1].copy()
            other_rows = group.iloc[1:].copy() if len(group) > 1 else pd.DataFrame()

            # 清除非保留字段
            for col in other_rows.columns:
                if col not in keep_cols:
                    other_rows[col] = ''

            source = first_row['Source'].iloc[0]
            number = first_row['Number'].iloc[0]

            # 根据开关决定是否更新采购订单状态
            if ENABLE_STATUS_UPDATE and pg_conn:
                if update_purchase_order_state(pg_conn, source, success_log_file, error_log_file):
                    success_count_status += 1
                else:
                    error_count_status += 1

            # 根据开关决定是否关联 Bill 和 PO
            if ENABLE_BILL_PO_LINKING and pg_conn:
                if link_bill_and_po(pg_conn, source, number, success_log_file, error_log_file):
                    success_count_link += 1
                else:
                    error_count_link += 1

            result = pd.concat([first_row, other_rows]) if not other_rows.empty else first_row
            return result

        df = grouped.apply(process_group).reset_index(drop=True)

        # 记录统计信息到日志文件
        with open(success_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n总结统计信息:\n")
            if ENABLE_STATUS_UPDATE:
                f.write(f"成功更新状态的采购订单数量: {success_count_status}\n")
                f.write(f"更新状态失败的采购订单数量: {error_count_status}\n")
            if ENABLE_BILL_PO_LINKING:
                f.write(f"成功关联Bill和PO的数量: {success_count_link}\n")
                f.write(f"关联Bill和PO失败的数量: {error_count_link}\n")

    return df

def update_purchase_order_state(conn, source, success_log_file, error_log_file):
    """更新PostgreSQL数据库中的采购订单状态
    Args:
        conn: PostgreSQL数据库连接
        source: 采购订单编号
        success_log_file: 成功日志文件路径
        error_log_file: 失败日志文件路径
    """
    cursor = None
    update_query = f"""
    UPDATE purchase_order
    SET state = 'done'
    WHERE name = '{source}'
    """
    try:
        cursor = conn.cursor()
        print(f"正在执行SQL更新状态: {update_query}")
        cursor.execute(update_query)
        conn.commit()

        # 记录成功日志
        with open(success_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 成功更新采购订单 {source} 的状态\n")
            f.write(f"执行的SQL: {update_query}\n\n")

        return True
    except Exception as e:
        # 记录失败日志
        with open(error_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 更新采购订单 {source} 的状态时出错: {e}\n")
            f.write(f"执行的SQL: {update_query}\n\n")

        conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def link_bill_and_po(conn, source, number, success_log_file, error_log_file):
    """关联PostgreSQL数据库中的供应商账单和采购订单
    Args:
        conn: PostgreSQL数据库连接
        source: 采购订单编号 (e.g., P012345)
        number: 供应商账单编号 (e.g., B012345)
        success_log_file: 成功日志文件路径
        error_log_file: 失败日志文件路径
    """
    cursor = None
    purchase_order_id = None
    account_move_id = None
    sql_executed = []

    try:
        cursor = conn.cursor()

        # 第一步：查询ID
        print(f"关联已存在: PO ID {source}, Bill ID {number}")
        query_po_id = f"SELECT id FROM purchase_order WHERE name = '{source}'"
        sql_executed.append(query_po_id)
        cursor.execute(query_po_id)
        po_result = cursor.fetchone()
        if not po_result:
            raise Exception(f"未找到采购订单 {source}")
        purchase_order_id = po_result[0]

        query_bill_id = f"SELECT id FROM account_move WHERE name = '{number}' AND move_type = 'in_invoice'"
        sql_executed.append(query_bill_id)
        cursor.execute(query_bill_id)
        bill_result = cursor.fetchone()
        if not bill_result:
            raise Exception(f"未找到供应商账单 {number}")
        account_move_id = bill_result[0]

        # 检查关联是否已存在
        check_rel_query = f"SELECT 1 FROM account_move_purchase_order_rel WHERE purchase_order_id = {purchase_order_id} AND account_move_id = {account_move_id}"
        sql_executed.append(check_rel_query)
        cursor.execute(check_rel_query)
        if cursor.fetchone():
            print(f"关联已存在: PO ID {purchase_order_id}, Bill ID {account_move_id}")
            # 记录已存在日志
            with open(success_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] PO {source} (ID: {purchase_order_id}) 和 Bill {number} (ID: {account_move_id}) 的关联已存在，跳过插入。\n")
        else:
            # 插入关联记录
            insert_rel_query = f"INSERT INTO account_move_purchase_order_rel (purchase_order_id, account_move_id) VALUES ({purchase_order_id}, {account_move_id})"
            sql_executed.append(insert_rel_query)
            cursor.execute(insert_rel_query)
            print(f"成功插入关联: PO ID {purchase_order_id}, Bill ID {account_move_id}")

        # 第二步：更新采购订单的invoice_count
        update_po_count_query = f"UPDATE purchase_order SET invoice_count = 1 WHERE id = {purchase_order_id}"
        sql_executed.append(update_po_count_query)
        cursor.execute(update_po_count_query)
        print(f"成功更新 PO {source} 的 invoice_count")

        # 第三步：更新 account_move_line 的 purchase_line_id
        update_aml_query = f"""
        WITH matched_lines AS (
            SELECT
                aml.id AS account_move_line_id,
                pol.id AS purchase_order_line_id
            FROM account_move_line aml
            JOIN purchase_order_line pol ON aml.product_id = pol.product_id
                                        AND aml.quantity = pol.product_qty
                                        AND pol.order_id = {purchase_order_id}
            WHERE aml.move_id = {account_move_id}
            AND aml.purchase_line_id IS NULL -- 只更新尚未关联的行
        )
        UPDATE account_move_line
        SET purchase_line_id = ml.purchase_order_line_id
        FROM matched_lines ml
        WHERE account_move_line.id = ml.account_move_line_id;
        """
        sql_executed.append(update_aml_query)
        cursor.execute(update_aml_query)
        print(f"成功更新 Bill {number} 中匹配行的 purchase_line_id")

        conn.commit()

        # 记录成功日志
        with open(success_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 成功关联 PO {source} (ID: {purchase_order_id}) 和 Bill {number} (ID: {account_move_id})\n")
            for sql in sql_executed:
                f.write(f"  执行的SQL: {sql.strip()}\n")
            f.write("\n")

        return True
    except Exception as e:
        # 记录失败日志
        with open(error_log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"[{timestamp}] 关联 PO {source} 和 Bill {number} 时出错: {e}\n")
            for sql in sql_executed:
                f.write(f"  尝试执行的SQL: {sql.strip()}\n")
            f.write("\n")

        conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()

def save_to_file(df, filename='purchase_bill_template'):
    """将数据保存为CSV和Excel文件"""
    try:
        # 获取当前时间戳并格式化
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(__file__)

        # 创建backups文件夹如果不存在
        backups_dir = os.path.join(script_dir, "backups")
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
            print(f"创建backups文件夹: {backups_dir}")

        # 移动旧文件到backups文件夹
        for f in os.listdir(script_dir):
            if f.startswith(filename) and (f.endswith('.csv') or f.endswith('.xlsx')):
                src = os.path.join(script_dir, f)
                dst = os.path.join(backups_dir, f)
                shutil.move(src, dst)
                print(f"移动旧文件到backups文件夹: {src} -> {dst}")

        # CSV文件路径
        csv_path = os.path.join(script_dir, f"{filename}_{timestamp}.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"数据已成功保存为CSV文件: {csv_path}")

        # Excel文件路径
        excel_path = os.path.join(script_dir, f"{filename}_{timestamp}.xlsx")
        df.to_excel(excel_path, index=False)
        print(f"数据已成功保存为Excel文件: {excel_path}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    sql_engine = None
    pg_conn = None
    try:
        # 1. 连接SQL Server数据库
        sql_engine = connect_to_sql_server()
        if not sql_engine:
            raise Exception("无法连接到SQL Server数据库")

        # 2. 连接PostgreSQL数据库
        pg_conn = connect_to_postgresql()
        if not pg_conn:
            raise Exception("无法连接到PostgreSQL数据库")

        # 3. 获取数据
        df = fetch_purchase_data(sql_engine)
        if df is not None:
            # 4. 转换格式并根据开关执行数据库操作
            odoo_df = convert_to_odoo_format(df, pg_conn)
            # 5. 根据开关决定是否保存为CSV和Excel
            if ENABLE_FILE_EXPORT:
                save_to_file(odoo_df)
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        # 关闭数据库连接
        if sql_engine:
            sql_engine.dispose()
            print("已关闭SQL Server连接")
        if pg_conn:
            pg_conn.close()
            print("已关闭PostgreSQL连接")