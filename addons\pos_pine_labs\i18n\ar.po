# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_pine_labs
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2025-05-09 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_client
msgid "A client id issued directly to the merchant by Pine Labs."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_merchant
msgid "A merchant id issued directly to the merchant by Pine Labs."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_security_token
msgid "A security token issued directly to the merchant by Pine Labs."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_store
msgid "A store id issued directly to the merchant by Pine Labs."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_allowed_payment_mode
msgid "Accepted payment modes by Pine Labs for transactions."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields.selection,name:pos_pine_labs.selection__pos_payment_method__pine_labs_allowed_payment_mode__all
msgid "All"
msgstr "الكل"

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Cannot process transactions with negative amount."
msgstr "لا يمكن معالجة المعاملات التي بها مبلغ قيمته سالبة. "

#. module: pos_pine_labs
#: model:ir.model.fields.selection,name:pos_pine_labs.selection__pos_payment_method__pine_labs_allowed_payment_mode__card
msgid "Card"
msgstr "البطاقة"

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_allowed_payment_mode
msgid "Pine Labs Allowed Payment Modes"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_client
msgid "Pine Labs Client ID"
msgstr ""

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Pine Labs Error"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_merchant
msgid "Pine Labs Merchant ID"
msgstr ""

#. module: pos_pine_labs
#. odoo-python
#: code:addons/pos_pine_labs/models/pos_payment_method.py:0
msgid ""
"Pine Labs POS transaction cancelled. Retry again for collecting payment."
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment__pine_labs_plutus_transaction_ref
msgid "Pine Labs PlutusTransactionReferenceID"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_security_token
msgid "Pine Labs Security Token"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_store
msgid "Pine Labs Store ID"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,field_description:pos_pine_labs.field_pos_payment_method__pine_labs_test_mode
msgid "Pine Labs Test Mode"
msgstr ""

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Pine Labs get payment status request failed"
msgstr ""

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Pine Labs make payment request failed"
msgstr ""

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Pine Labs payment cancellation request failed"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model,name:pos_pine_labs.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "طرق الدفع في نقطة البيع "

#. module: pos_pine_labs
#: model:ir.model,name:pos_pine_labs.model_pos_payment
msgid "Point of Sale Payments"
msgstr "مدفوعات نقطة البيع "

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Reference number mismatched"
msgstr "الرقم المرجعي غير متطابق "

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment__pine_labs_plutus_transaction_ref
msgid ""
"Required during the refund order process: "
"https://developer.pinelabs.com/in/instore/cloud-integration#Example-JSON-"
"request-for-Void-ICB-on-UPI-transaction"
msgstr ""

#. module: pos_pine_labs
#: model:ir.model.fields,help:pos_pine_labs.field_pos_payment_method__pine_labs_test_mode
msgid "Test Pine Labs transaction process."
msgstr ""

#. module: pos_pine_labs
#. odoo-python
#: code:addons/pos_pine_labs/models/pos_payment_method.py:0
msgid ""
"The expected error code for the Pine Labs POS status request was not "
"included in the response."
msgstr ""

#. module: pos_pine_labs
#. odoo-python
#: code:addons/pos_pine_labs/models/pos_payment_method.py:0
msgid "This Payment Terminal is only valid for INR Currency"
msgstr "جهاز الدفع هذا صالح فقط لعملة الروبية الهندية "

#. module: pos_pine_labs
#. odoo-javascript
#: code:addons/pos_pine_labs/static/src/app/utils/payment/payment_pine_labs.js:0
msgid "Transaction failed due to inactivity"
msgstr "Transaction failed due to inactivity"

#. module: pos_pine_labs
#: model:ir.model.fields.selection,name:pos_pine_labs.selection__pos_payment_method__pine_labs_allowed_payment_mode__upi
msgid "Upi"
msgstr "Upi"
